<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动条修复测试</title>
    <style>
        /* 基础样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }

        /* 页面内容 - 故意设置很高以产生滚动条 */
        .page-content {
            height: 200vh;
            padding: 20px;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
        }

        /* 模拟图示视图容器 */
        .graph-view-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background-color: rgba(255, 255, 255, 0.95);
            z-index: 1000;
            padding: 20px;
            overflow-y: auto;
            display: none;
            scrollbar-width: thin;
            scrollbar-color: #2989d8 #f0f0f0;
        }

        .graph-view-container::-webkit-scrollbar {
            width: 10px;
        }

        .graph-view-container::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 10px;
        }

        .graph-view-container::-webkit-scrollbar-thumb {
            background: #2989d8;
            border-radius: 10px;
        }

        /* 图示视图内容 - 故意设置很高以产生滚动条 */
        .graph-content {
            height: 150vh;
            background: linear-gradient(to bottom, #fff3e0, #ffe0b2);
            border-radius: 10px;
            padding: 20px;
        }

        /* 关键修复：当图示视图激活时，隐藏页面body的滚动条 */
        body.graph-view-active {
            overflow: hidden !important;
        }

        /* 当表格视图激活时，确保页面body有滚动条 */
        body.table-view-active {
            overflow: auto !important;
        }

        /* 按钮样式 */
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 2000;
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #2989d8;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* 状态指示器 */
        .status {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 2000;
            padding: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            border-radius: 5px;
            font-size: 14px;
        }

        /* 内容区域样式 */
        .content-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1, h2 {
            color: #333;
        }

        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body class="table-view-active">
    <!-- 控制按钮 -->
    <div class="controls">
        <button class="btn btn-primary" onclick="showGraphView()">显示图示视图</button>
        <button class="btn btn-secondary" onclick="showTableView()">显示表格视图</button>
    </div>

    <!-- 状态指示器 -->
    <div class="status" id="status">
        当前视图：表格视图 | Body滚动条：启用
    </div>

    <!-- 页面内容（表格视图） -->
    <div class="page-content">
        <div class="content-section">
            <h1>滚动条修复测试页面</h1>
            <p>这个页面用于测试双滚动条问题的修复效果。</p>
        </div>

        <div class="content-section">
            <h2>问题描述</h2>
            <p>原来的问题是：当图示视图激活时，页面右侧会出现<span class="highlight">两个滚动条</span>：</p>
            <ul>
                <li>一个是页面body的滚动条（无用的）</li>
                <li>一个是图示视图容器的滚动条（实际起作用的）</li>
            </ul>
        </div>

        <div class="content-section">
            <h2>修复方案</h2>
            <p>通过CSS规则实现：</p>
            <ul>
                <li>当图示视图激活时：<code>body.graph-view-active { overflow: hidden !important; }</code></li>
                <li>当表格视图激活时：<code>body.table-view-active { overflow: auto !important; }</code></li>
            </ul>
        </div>

        <div class="content-section">
            <h2>测试步骤</h2>
            <ol>
                <li>当前是表格视图，页面有滚动条，可以正常滚动</li>
                <li>点击"显示图示视图"按钮</li>
                <li>观察：页面body的滚动条应该消失，只保留图示视图内部的滚动条</li>
                <li>点击"显示表格视图"按钮</li>
                <li>观察：页面body的滚动条应该恢复</li>
            </ol>
        </div>

        <div class="content-section">
            <h2>填充内容</h2>
            <p>这里是一些填充内容，用于确保页面有足够的高度产生滚动条。</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>

        <div class="content-section">
            <h2>更多内容</h2>
            <p>继续添加内容以确保页面足够长...</p>
            <p>这样我们就能看到滚动条的效果了。</p>
            <p>请尝试滚动页面，然后切换到图示视图，观察滚动条的变化。</p>
        </div>
    </div>

    <!-- 图示视图容器 -->
    <div class="graph-view-container" id="graphView">
        <div class="graph-content">
            <div class="content-section">
                <h1>图示视图</h1>
                <p>这是图示视图的内容。注意这个容器是固定定位的，覆盖整个页面。</p>
            </div>

            <div class="content-section">
                <h2>滚动条测试</h2>
                <p>这个视图的内容也很长，会产生滚动条。</p>
                <p>但是现在页面body的滚动条应该被隐藏了，只有这个容器内部的滚动条起作用。</p>
            </div>

            <div class="content-section">
                <h2>修复效果</h2>
                <p>如果修复成功，你应该只能看到<span class="highlight">一个滚动条</span>，就是这个图示视图容器的滚动条。</p>
                <p>页面body的滚动条应该被隐藏了。</p>
            </div>

            <div class="content-section">
                <h2>更多图示内容</h2>
                <p>这里是更多的图示视图内容...</p>
                <p>继续滚动以测试滚动条功能。</p>
                <p>确保只有一个滚动条在工作。</p>
            </div>
        </div>
    </div>

    <script>
        function showGraphView() {
            // 显示图示视图
            document.getElementById('graphView').style.display = 'block';
            
            // 切换body类
            document.body.classList.remove('table-view-active');
            document.body.classList.add('graph-view-active');
            
            // 更新状态
            updateStatus('图示视图', '禁用');
        }

        function showTableView() {
            // 隐藏图示视图
            document.getElementById('graphView').style.display = 'none';
            
            // 切换body类
            document.body.classList.remove('graph-view-active');
            document.body.classList.add('table-view-active');
            
            // 更新状态
            updateStatus('表格视图', '启用');
        }

        function updateStatus(viewType, scrollStatus) {
            document.getElementById('status').textContent = 
                `当前视图：${viewType} | Body滚动条：${scrollStatus}`;
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === '1') {
                showTableView();
            } else if (e.key === '2') {
                showGraphView();
            }
        });
    </script>
</body>
</html>
